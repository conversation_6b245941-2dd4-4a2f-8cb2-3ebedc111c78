/**
 * Negozio Web Component
 * A pure JavaScript web component for the negozio (shop) view
 */

// Import i18n for translations
import { i18n } from '@/i18n/index.js'

// Helper function to get translations
function $t(key, params = {}) {
  if (i18n && i18n.t) {
    return i18n.t(key, params)
  }
  // Fallback if i18n is not available
  return key
}
class NegozioWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      loading: false,
      products: [],
      user: null,
      apiParams: null, // For mobile API parameters
      authToken: null,
      ccbCount: 0
    }

    // Track if we've already triggered initial load to prevent duplicates
    this.hasTriggeredInitialLoad = false

    // Bind methods
    this.handleProductClick = this.handleProductClick.bind(this)
    this.loadProducts = this.loadProducts.bind(this)
  }

  // Define observed attributes
  static get observedAttributes() {
    return ['user-data', 'auth-token', 'loading', 'api-params']
  }

  // Lifecycle: Component connected to DOM
  async connectedCallback() {
    this.render()
    this.setupEventListeners()
    // Import and setup cart component
    await this.setupCartComponent()
    // Products will be loaded when attributes are set via attributeChangedCallback
    this.checkAndLoadProductsOnce()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Lifecycle: Attribute changed
  attributeChangedCallback(name, oldValue, newValue) {
    if (oldValue !== newValue) {
      switch (name) {
        case 'user-data':
          this.state.user = newValue ? JSON.parse(newValue) : null
          break
        case 'auth-token':
          console.log('authToken changed to', newValue)
          this.state.authToken = newValue
          break
        case 'loading':
          this.state.loading = newValue === 'true'
          break
        case 'api-params':
          if (newValue && newValue !== '{}') {
            this.state.apiParams = JSON.parse(newValue)
          } else {
            this.state.apiParams = null
          }
          break
      }
    }
  }

  // Check if all required data is available and load products once
  checkAndLoadProductsOnce() {
    if (
      !this.state.loading &&
      this.state.products.length === 0 &&
      this.state.authToken &&
      !this.hasTriggeredInitialLoad
    ) {
      // Set flag immediately to prevent race conditions from multiple attribute changes
      this.hasTriggeredInitialLoad = true

      console.log('All required data available, loading products once', {
        authToken: !!this.state.authToken,
        apiParams: this.state.apiParams
      })
      this.loadProducts()
      console.log('loading products')
    }
  }

  // Public method for explicit user-driven reload
  reloadProducts() {
    console.log('User-driven product reload requested')
    this.hasTriggeredInitialLoad = false
    this.state.products = []
    this.loadProducts()
  }

  // Load products from API or mock data
  async loadProducts() {
    console.log('=== loadProducts called ===', {
      loading: this.state.loading,
      productsCount: this.state.products.length,
      hasTriggeredInitialLoad: this.hasTriggeredInitialLoad
    })

    this.state.loading = true
    this.render()

    try {
      let products = []

      // Check if we have the necessary parameters for API call
      if (
        this.state.apiParams &&
        this.state.apiParams.studentId &&
        this.state.apiParams.schoolYear &&
        this.state.authToken
      ) {
        console.log('Using API call with parameters:', {
          studentId: this.state.apiParams.studentId,
          schoolYear: this.state.apiParams.schoolYear,
          nextApiUrl: this.state.apiParams.nextApiUrl,
          hasToken: !!this.state.authToken
        })

        try {
          const {
            loadProductsFromAPI,
            transformApiProducts
          } = await import('/src/data/negozio.js')

          const response = await loadProductsFromAPI(
            this.state.authToken,
            this.state.apiParams.studentId,
            this.state.apiParams.schoolYear,
            this.state.apiParams.nextApiUrl // Will be null for desktop, which is handled in loadProductsFromAPI
          )

          if (response.data && response.data.esito === 'OK') {
            products = transformApiProducts(response.data)
            console.log('Transformed products:', products)
          } else {
            const errorMessage =
              (response.data &&
                response.data.info &&
                response.data.info.messaggio) ||
              $t('negozio.errors.unknown_error')
            throw new Error(
              $t('negozio.errors.api_response_error', { message: errorMessage })
            )
          }
        } catch (apiError) {
          console.error('API error:', apiError)

          // If it's a CORS error or network error, fall back to mock data
          if (
            apiError.message.includes('CORS') ||
            apiError.message.includes('Network Error') ||
            apiError.code === 'ERR_NETWORK'
          ) {
            console.warn($t('negozio.errors.cors_fallback'))
            try {
              const { getMockProducts } = await import('/src/data/negozio.js')
              const mockResponse = await getMockProducts()
              products = mockResponse.data.products || []
            } catch (importError) {
              console.error('Failed to load mock products:', importError)
              // Skip loading mock products entirely if import fails
              products = []
            }
          } else {
            throw new Error($t('negozio.errors.server_error'))
          }
        }
      } else {
        // Missing required parameters - show empty state
        console.warn('Missing required API parameters:', {
          hasApiParams: !!this.state.apiParams,
          hasStudentId: !!(
            this.state.apiParams && this.state.apiParams.studentId
          ),
          hasSchoolYear: !!(
            this.state.apiParams && this.state.apiParams.schoolYear
          ),
          hasAuthToken: !!this.state.authToken,
          apiParams: this.state.apiParams
        })
        products = []
      }

      this.state.products = products
    } catch (error) {
      console.error('Error loading products:', error)
      this.dispatchEvent(
        new CustomEvent('error', {
          detail: {
            message: error.message || $t('negozio.errors.loading_failed')
          }
        })
      )
    } finally {
      this.state.loading = false
      this.render()
    }
  }
  // Handle product click - show product detail in modal
  handleProductClick(product) {
    // Import and create modal and product detail components
    this.showProductModal(product)
  }

  // Show product in modal
  async showProductModal(product) {
    try {
      // Import components
      await import('./ModalWebComponent.js')
      await import('./ProductDetailWebComponent.js')

      // Create modal if it doesn't exist
      let modal = document.querySelector('modal-web-component')
      if (!modal) {
        modal = document.createElement('modal-web-component')
        document.body.appendChild(modal)
      }

      // Create product detail component
      const productDetail = document.createElement(
        'product-detail-web-component'
      )
      productDetail.setProduct(product)

      // Pass authentication parameters to product detail component
      if (this.state.authToken) {
        productDetail.setAttribute('auth-token', this.state.authToken)
      }
      if (this.state.apiParams) {
        productDetail.setAttribute('api-params', JSON.stringify(this.state.apiParams))
      }

      // Handle add to cart success from product detail
      productDetail.addEventListener('add-to-cart-success', event => {
        console.log('Product added to cart successfully:', event.detail)
        // You can add additional logic here, such as updating cart UI
        // or showing a global success message
        modal.close()
      })

      // Handle add to cart error from product detail
      productDetail.addEventListener('add-to-cart-error', event => {
        console.error('Error adding product to cart:', event.detail)
        // The error is already displayed in the product detail component
        // You can add additional global error handling here if needed
      })

      // Handle legacy add to cart event (for backward compatibility)
      productDetail.addEventListener('add-to-cart', event => {
        console.log('Legacy add to cart event:', event.detail)
        // This event is now handled by the new API-based approach
        // but we keep this listener for backward compatibility
      })

      // Clear modal content and add product detail
      modal.innerHTML = ''
      modal.appendChild(productDetail)

      // Open modal with product name as title
      modal.open({
        title: product.name,
        showCloseButton: true,
        showFooter: false
      })
    } catch (error) {
      console.error('Error showing product modal:', error)
    }
  }

  // Setup cart component
  async setupCartComponent() {
    try {
      // Import the cart component
      await import('./CarrelloWebComponent.js')

      // Create cart component if it doesn't exist
      if (!this.cartComponent) {
        this.cartComponent = document.createElement('carrello-web-component')
        document.body.appendChild(this.cartComponent)
      }
    } catch (error) {
      console.error('Error setting up cart component:', error)
    }
  }

  // Handle add to cart
  handleAddToCart(cartItem) {
    console.log('Product added to cart:', cartItem)

    // Add item to cart component
    if (this.cartComponent) {
      this.cartComponent.addItem(cartItem)
    }

    // Dispatch add to cart event for parent components to handle
    this.dispatchEvent(
      new CustomEvent('product-added-to-cart', {
        detail: cartItem,
        bubbles: true
      })
    )
  }

  // Setup event listeners
  setupEventListeners() {
    // Add any global event listeners here
  }

  // Cleanup
  cleanup() {
    // Remove event listeners and cleanup resources
    if (this.cartComponent && this.cartComponent.parentNode) {
      this.cartComponent.parentNode.removeChild(this.cartComponent)
      this.cartComponent = null
    }
  }
  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: block;
          font-family: 'Ubuntu', sans-serif;
          padding: 20px;
          background-color: #fff;
        }

        .negozio-container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .negozio-header {
          margin-bottom: 30px;
        }

        .negozio-title {
          font-size: 2rem;
          font-weight: bold;
          color: #333;
          margin-bottom: 10px;
        }

        .negozio-subtitle {
          color: #666;
          font-size: 1.1rem;
        }

        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
          font-size: 1.2rem;
          color: #666;
        }

        .products-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 20px;
          margin-top: 20px;
        }

        .product-card {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 20px;
          background: #fff;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          transition: transform 0.2s, box-shadow 0.2s;
          cursor: pointer;
        }

        .product-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .product-image {
          width: 100%;
          height: 150px;
          background: #f5f5f5;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 15px;
          color: #999;
        }

        .product-name {
          font-size: 1.4rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 10px;
          line-height: 1.3;
        }

        .product-description {
          color: #555;
          font-size: 1.1rem;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .product-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .product-price {
          font-size: 1.3rem;
          font-weight: bold;
          color: #2c5aa0;
        }

        .product-price.discounted {
          color: #e74c3c;
        }

        .product-original-price {
          font-size: 1rem;
          color: #999;
          text-decoration: line-through;
          margin-left: 8px;
        }

        .product-discount-badge {
          background: #e74c3c;
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.7rem;
          font-weight: bold;
          margin-left: 8px;
        }

        .product-price-container {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }

        .product-options {
          font-size: 1rem;
          color: #666;
          margin-bottom: 8px;
          font-style: italic;
        }

        .product-category {
          background: #ecc506;
          color: #333;
          padding: 8px 14px;
          border-radius: 15px;
          font-size: 1rem;
          font-weight: 500;
          font-family: 'Ubuntu', sans-serif;
        }

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #666;
        }

        .empty-state-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          opacity: 0.5;
        }

        @media (max-width: 768px) {
          .products-grid {
            grid-template-columns: 1fr;
          }

          .negozio-title {
            font-size: 1.5rem;
          }
        }
      </style>
    `
  }
  // Render component
  render() {
    const styles = this.getStyles()

    let content = ''
    if (this.state.loading) {
      content = `
        <div class="loading-spinner">
          <div>${$t('negozio.loading')}</div>
        </div>
      `
    } else if (this.state.products.length === 0) {
      content = `
        <div class="empty-state">
          <div class="empty-state-icon">🛍️</div>
          <h3>${$t('negozio.empty_state.title')}</h3>
          <p>${$t('negozio.empty_state.message')}</p>
        </div>
      `
    } else {
      const productsHtml = this.state.products
        .map(product => {
          // Handle pricing display
          let priceHtml = `<div class="product-price${
            product.discountActive ? ' discounted' : ''
          }">€${product.price.toFixed(2)}</div>`

          if (product.discountActive && product.originalPrice > product.price) {
            priceHtml += `<div class="product-original-price">€${product.originalPrice.toFixed(
              2
            )}</div>`
            priceHtml += `<div class="product-discount-badge">${$t(
              'negozio.product.discount_badge'
            )}</div>`
          }

          // Handle characteristics display
          let characteristicsHtml = ''
          if (
            product.characteristics &&
            product.characteristics.options &&
            product.characteristics.options.length > 0
          ) {
            const optionCount = product.characteristics.options.length
            characteristicsHtml = `<div class="product-options">${$t(
              'negozio.product.options_available',
              { count: optionCount }
            )}</div>`
          }
          // Handle product image display
          let imageHtml = ''
          if (
            product.immagini &&
            Array.isArray(product.immagini) &&
            product.immagini.length > 0
          ) {
            // Show first image from immagini array
            const firstImage = product.immagini[0]
            // Handle both string URLs and objects with url property
            const imageUrl =
              typeof firstImage === 'string' ? firstImage : firstImage.url
            imageHtml = `<img src="${imageUrl}" alt="${
              product.name
            }" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;" />`
          } else {
            // Fallback to emoji placeholder
            imageHtml = `📦 ${product.name}`
          }

          return `
            <div class="product-card" data-product-id="${
              product.id
            }" style="background-color: ${product.backgroundColor};
                color: ${product.textColor};">
              <div class="product-image">
                ${imageHtml}
              </div>
              <div class="product-name" style="color: ${product.textColor};">${
            product.name
          }</div>
              <div class="product-description" style="color: ${
                product.textColor
              }; opacity: 0.8;">${product.description}</div>
              ${characteristicsHtml}
              <div class="product-footer">
                <div class="product-price-container">
                  ${priceHtml}
                </div>
                <div class="product-category">${product.category}</div>
              </div>
            </div>
          `
        })
        .join('')

      content = `
        <div class="products-grid">
          ${productsHtml}
        </div>
      `
    }

    this.shadowRoot.innerHTML = `
      ${styles}
      <div class="negozio-container">
        <div class="negozio-header">
          <h1 class="negozio-title">${$t('negozio.title')}</h1>
          <p class="negozio-subtitle">${$t('negozio.subtitle')}</p>
        </div>
        ${content}
      </div>
    `

    // Add event listeners to product cards
    if (!this.state.loading && this.state.products.length > 0) {
      this.shadowRoot.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('click', () => {
          const productId = parseInt(card.dataset.productId)
          const product = this.state.products.find(p => p.id === productId)
          if (product) {
            this.handleProductClick(product)
          }
        })
      })
    }
  }
}

// Register the custom element
if (!customElements.get('negozio-web-component')) {
  customElements.define('negozio-web-component', NegozioWebComponent)
}

export default NegozioWebComponent
