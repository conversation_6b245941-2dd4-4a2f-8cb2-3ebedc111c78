/**
 * ProductDetailWebComponent - Displays detailed product information
 * Used within modals or other containers to show product details
 */
class ProductDetailWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      product: null,
      selectedOptions: {},
      quantity: 1,
      // Authentication and API parameters
      authToken: null,
      apiParams: null,
      // Add to cart state
      addingToCart: false,
      addToCartError: null,
      addToCartSuccess: null
    }

    // Bind methods
    this.handleOptionChange = this.handleOptionChange.bind(this)
    this.handleQuantityChange = this.handleQuantityChange.bind(this)
    this.handleAddToCart = this.handleAddToCart.bind(this)
  }

  // Observed attributes
  static get observedAttributes() {
    return ['product-data', 'auth-token', 'api-params']
  }

  // Handle attribute changes
  attributeChangedCallback(name, oldValue, newValue) {
    if (oldValue !== newValue) {
      switch (name) {
        case 'product-data':
          if (newValue) {
            try {
              this.state.product = JSON.parse(newValue)
              this.render()
            } catch (error) {
              console.error('Invalid product data:', error)
            }
          }
          break
        case 'auth-token':
          this.state.authToken = newValue
          break
        case 'api-params':
          if (newValue && newValue !== '{}') {
            try {
              this.state.apiParams = JSON.parse(newValue)
            } catch (error) {
              console.error('Invalid API params:', error)
              this.state.apiParams = null
            }
          } else {
            this.state.apiParams = null
          }
          break
      }
    }
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.render()
  }

  // Set product data programmatically
  setProduct(product) {
    this.state.product = product
    this.state.selectedOptions = {}
    this.state.quantity = 1
    this.render()
  }

  // Handle option selection
  handleOptionChange(event) {
    const { name, value } = event.target
    this.state.selectedOptions[name] = value
    this.render()
  }

  // Calculate effective product availability based on selected options
  getEffectiveAvailability() {
    const product = this.state.product
    if (!product) return false

    // If product has no options, use base availability
    if (!product.characteristics || !product.characteristics.options) {
      return product.available
    }

    // If options are required but none selected, product is not available
    if (
      product.characteristics.required &&
      !this.state.selectedOptions.characteristic
    ) {
      return false
    }

    // If an option is selected, check its availability
    if (this.state.selectedOptions.characteristic) {
      const selectedOptionId = this.state.selectedOptions.characteristic
      const selectedOption = product.characteristics.options.find(
        option => option.label === selectedOptionId
      )

      if (selectedOption) {
        return selectedOption.available > 0
      }
    }

    // If no option is selected but options are not required, use base availability
    return product.available
  }

  // Handle quantity change
  handleQuantityChange(event) {
    const quantity = parseInt(event.target.value) || 1
    this.state.quantity = Math.max(1, quantity)
    this.render()
  }

  // Handle add to cart
  async handleAddToCart() {
    if (!this.state.product) return

    // Check if we have the necessary authentication parameters
    if (!this.state.authToken || !this.state.apiParams) {
      console.error('Missing authentication parameters for add to cart')
      this.showAddToCartError('Authentication parameters missing')
      return
    }

    // Prevent multiple simultaneous requests
    if (this.state.addingToCart) return

    // Clear previous messages
    this.state.addToCartError = null
    this.state.addToCartSuccess = null
    this.state.addingToCart = true
    this.render()

    try {
      // Prepare cart item data according to API specification
      const cartItemData = {
        id_marketplace: this.state.product.id,
        quantita: this.state.quantity
      }

      // Add selected options if any
      if (Object.keys(this.state.selectedOptions).length > 0) {
        cartItemData.opzioni = this.state.selectedOptions
      }

      // Import the API function dynamically
      const { addProductToCart } = await import('/src/data/negozio.js')

      // Make the API call
      const response = await addProductToCart(
        this.state.authToken,
        this.state.apiParams.studentId,
        this.state.apiParams.schoolYear,
        this.state.apiParams.nextApiUrl,
        cartItemData
      )

      // Check if the response indicates success
      if (response.data && response.data.esito === 'OK') {
        // Show success message
        const successMessage = response.data.info?.messaggio || 'Product added to cart successfully'
        this.showAddToCartSuccess(successMessage)

        // Trigger button animation
        this.animateAddToCartButton()

        // Dispatch add to cart success event with API response data
        this.dispatchEvent(
          new CustomEvent('add-to-cart-success', {
            detail: {
              product: this.state.product,
              quantity: this.state.quantity,
              selectedOptions: this.state.selectedOptions,
              apiResponse: response.data
            },
            bubbles: true
          })
        )
      } else {
        // Handle API error response
        const errorMessage = response.data?.info?.messaggio || 'Failed to add product to cart'
        this.showAddToCartError(errorMessage)
      }
    } catch (error) {
      console.error('Error adding product to cart:', error)

      // Handle different types of errors
      let errorMessage = 'Failed to add product to cart'
      if (error.response) {
        // Server responded with error status
        if (error.response.data?.info?.messaggio) {
          errorMessage = error.response.data.info.messaggio
        } else if (error.response.status === 401) {
          errorMessage = 'Authentication failed. Please log in again.'
        } else if (error.response.status === 403) {
          errorMessage = 'You do not have permission to add this product to cart.'
        } else if (error.response.status >= 500) {
          errorMessage = 'Server error. Please try again later.'
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection.'
      }

      this.showAddToCartError(errorMessage)

      // Dispatch add to cart error event
      this.dispatchEvent(
        new CustomEvent('add-to-cart-error', {
          detail: {
            product: this.state.product,
            error: error,
            message: errorMessage
          },
          bubbles: true
        })
      )
    } finally {
      this.state.addingToCart = false
      this.render()
    }
  }

  // Show add to cart success message
  showAddToCartSuccess(message) {
    this.state.addToCartSuccess = message
    this.state.addToCartError = null
    this.render()

    // Auto-hide success message after 3 seconds
    setTimeout(() => {
      if (this.state.addToCartSuccess === message) {
        this.state.addToCartSuccess = null
        this.render()
      }
    }, 3000)
  }

  // Show add to cart error message
  showAddToCartError(message) {
    this.state.addToCartError = message
    this.state.addToCartSuccess = null
    this.render()

    // Auto-hide error message after 5 seconds
    setTimeout(() => {
      if (this.state.addToCartError === message) {
        this.state.addToCartError = null
        this.render()
      }
    }, 5000)
  }

  // Animate the add to cart button
  animateAddToCartButton() {
    const button = this.shadowRoot.querySelector('.btn-add-to-cart')
    if (button) {
      // Add animation class
      button.classList.add('btn-add-to-cart-animate')

      // Remove animation class after animation completes
      setTimeout(() => {
        button.classList.remove('btn-add-to-cart-animate')
      }, 600) // Match the animation duration
    }
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: block;
          font-family: 'Ubuntu', sans-serif;
        }

        .product-detail {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .product-image-container {
          width: 100%;
          height: 250px;
          background: #f5f5f5;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }

        .product-image-container img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
        }

        .product-image-placeholder {
          font-size: 3rem;
          color: #999;
        }

        .product-info h2 {
          font-size: 1.5rem;
          color: #333;
          margin: 0 0 10px 0;
          font-weight: 600;
        }

        .product-description {
          color: #666;
          line-height: 1.6;
          margin-bottom: 15px;
        }

        .product-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 20px;
        }

        .product-category {
          background: #ecc506;
          color: #333;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 0.9rem;
          font-weight: 500;
        }

        .product-price {
          font-size: 1.8rem;
          font-weight: bold;
          color: #2c5aa0;
        }

        .product-price.discounted {
          color: #e74c3c;
        }

        .original-price {
          font-size: 1.2rem;
          color: #999;
          text-decoration: line-through;
          margin-left: 10px;
        }

        .selected-option {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 10px;
          margin-bottom: 15px;
          font-size: 0.9rem;
          color: #495057;
        }

        .selected-option strong {
          color: #333;
        }

        .product-stock {
          color: #666;
          font-size: 0.9rem;
          margin-bottom: 15px;
        }

        .product-stock.low-stock {
          color: #e74c3c;
          font-weight: 500;
        }

        .product-options {
          margin-bottom: 20px;
        }

        .option-group {
          margin-bottom: 15px;
        }

        .option-label {
          display: block;
          font-weight: 500;
          margin-bottom: 8px;
          color: #333;
        }

        .option-label.required::after {
          content: ' *';
          color: #e74c3c;
        }

        .option-select {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-family: 'Ubuntu', sans-serif;
          font-size: 1rem;
        }

        .quantity-section {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 20px;
        }

        .quantity-label {
          font-weight: 500;
          color: #333;
        }

        .quantity-input {
          width: 80px;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          text-align: center;
          font-family: 'Ubuntu', sans-serif;
        }

        .add-to-cart-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;
          padding-top: 10px;
        }

        .add-to-cart-messages {
          width: 100%;
          text-align: center;
        }

        .add-to-cart-success {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
          border-radius: 4px;
          padding: 10px;
          margin-bottom: 10px;
          font-size: 0.9rem;
          animation: fadeIn 0.3s ease-in;
        }

        .add-to-cart-error {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
          border-radius: 4px;
          padding: 10px;
          margin-bottom: 10px;
          font-size: 0.9rem;
          animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .btn-add-to-cart {
          background: #2c5aa0;
          color: white;
          border: none;
          padding: 12px 30px;
          border-radius: 6px;
          font-family: 'Ubuntu', sans-serif;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 150px;
          position: relative;
          overflow: hidden;
        }

        .btn-add-to-cart.loading {
          background: #6c757d;
          cursor: not-allowed;
        }

        .btn-add-to-cart:hover {
          background: #1e3d6f;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(44, 90, 160, 0.3);
        }

        .btn-add-to-cart:disabled {
          background: #ccc;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .btn-add-to-cart:disabled:hover {
          transform: none;
          box-shadow: none;
        }

        /* Add to cart animation */
        .btn-add-to-cart-animate {
          animation: addToCartPulse 0.6s ease-out;
        }

        @keyframes addToCartPulse {
          0% {
            transform: scale(1);
            background: #2c5aa0;
          }
          15% {
            transform: scale(0.95);
            background: #1e3d6f;
          }
          30% {
            transform: scale(1.05);
            background: #28a745;
            box-shadow: 0 0 20px rgba(40, 167, 69, 0.4);
          }
          50% {
            transform: scale(1.02);
            background: #28a745;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
          }
          100% {
            transform: scale(1);
            background: #2c5aa0;
            box-shadow: none;
          }
        }

        /* Success ripple effect */
        .btn-add-to-cart-animate::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.6);
          transform: translate(-50%, -50%);
          animation: ripple 0.6s ease-out;
        }

        @keyframes ripple {
          0% {
            width: 0;
            height: 0;
            opacity: 1;
          }
          100% {
            width: 100px;
            height: 100px;
            opacity: 0;
          }
        }

        @media (max-width: 768px) {
          .product-image-container {
            height: 200px;
          }

          .product-info h2 {
            font-size: 1.3rem;
          }

          .product-price {
            font-size: 1.5rem;
          }

          .product-meta {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      </style>
    `
  }

  // Render component
  render() {
    if (!this.state.product) {
      this.shadowRoot.innerHTML = `
        ${this.getStyles()}
        <div class="product-detail">
          <p>No product data available</p>
        </div>
      `
      return
    }

    const product = this.state.product
    const styles = this.getStyles()

    // Handle product image
    let imageHtml = ''
    if (
      product.immagini &&
      Array.isArray(product.immagini) &&
      product.immagini.length > 0
    ) {
      const firstImage = product.immagini[0]
      const imageUrl =
        typeof firstImage === 'string' ? firstImage : firstImage.url
      imageHtml = `<img src="${imageUrl}" alt="${product.name}" />`
    } else {
      imageHtml = `<div class="product-image-placeholder">📦</div>`
    }

    // Handle pricing
    let priceHtml = `<div class="product-price${
      product.discountActive ? ' discounted' : ''
    }">€${product.price.toFixed(2)}</div>`
    if (product.discountActive && product.originalPrice > product.price) {
      priceHtml += `<span class="original-price">€${product.originalPrice.toFixed(
        2
      )}</span>`
    }

    // Handle selected option display
    let selectedOptionHtml = ''
    let selectedOption = null
    if (
      this.state.selectedOptions.characteristic &&
      product.characteristics &&
      product.characteristics.options
    ) {
      const selectedOptionId = this.state.selectedOptions.characteristic
      selectedOption = product.characteristics.options.find(
        option => option.label === selectedOptionId
      )
      if (selectedOption) {
        selectedOptionHtml = `<div class="selected-option">
          <strong>${product.characteristics.description}:</strong> ${
          selectedOption.label
        }
        </div>`
      }
    }

    // Handle stock display
    let stockHtml = ''
    let displayStock = product.stock
    let stockMessage = ''

    // If an option is selected, show its availability instead of product stock
    if (selectedOption) {
      displayStock = selectedOption.available
    }

    if (displayStock !== undefined) {
      const stockClass = displayStock < 10 ? 'low-stock' : ''
      stockMessage =
        displayStock > 0 ? `${displayStock} disponibili` : 'Non disponibile'
      stockHtml = `<div class="product-stock ${stockClass}">
        ${stockMessage}
      </div>`
    }

    // Handle product options
    let optionsHtml = ''
    if (product.characteristics && product.characteristics.options) {
      const options = product.characteristics.options
      optionsHtml = `
        <div class="product-options">
          <div class="option-group">
            <label class="option-label ${
              product.characteristics.required ? 'required' : ''
            }">
              ${product.characteristics.description}
            </label>
            <select class="option-select" name="characteristic" onchange="this.getRootNode().host.handleOptionChange(event)">
              <option value="">Seleziona un'opzione</option>
              ${options
                .map(
                  option => `
                <option value="${option.label}" ${
                    option.available <= 0 ? 'disabled' : ''
                  } ${
                    this.state.selectedOptions.characteristic === option.label
                      ? 'selected'
                      : ''
                  }>
                  ${option.label} ${
                    option.available > 0
                      ? `(${option.available} disponibili)`
                      : '(Non disponibile)'
                  }
                </option>
              `
                )
                .join('')}
            </select>
          </div>
        </div>
      `
    }

    // Handle quantity selector (only if multiple orders allowed)
    let quantityHtml = ''
    if (product.multipleOrderAllowed) {
      // Calculate max quantity based on selected option or product stock
      let maxQuantity = product.stock || 99
      if (
        this.state.selectedOptions.characteristic &&
        product.characteristics &&
        product.characteristics.options
      ) {
        const selectedOptionId = this.state.selectedOptions.characteristic
        const selectedOption = product.characteristics.options.find(
          option => option.label === selectedOptionId
        )
        if (selectedOption) {
          maxQuantity = selectedOption.available || 0
        }
      }

      quantityHtml = `
        <div class="quantity-section">
          <label class="quantity-label">Quantità:</label>
          <input
            type="number"
            class="quantity-input"
            min="1"
            max="${maxQuantity}"
            value="${this.state.quantity}"
            onchange="this.getRootNode().host.handleQuantityChange(event)"
          />
        </div>
      `
    }

    this.shadowRoot.innerHTML = `
      ${styles}
      <div class="product-detail">
        <div class="product-image-container">
          ${imageHtml}
        </div>
        
        <div class="product-info">
          <h2>${product.name}</h2>
          <p class="product-description">${product.description}</p>
          
          <div class="product-meta">
            <span class="product-category">${product.category}</span>
            <div class="price-container">
              ${priceHtml}
            </div>
          </div>

          ${selectedOptionHtml}
          ${stockHtml}
          ${optionsHtml}
          ${quantityHtml}
          
          <div class="add-to-cart-section">
            <div class="add-to-cart-messages">
              ${this.state.addToCartSuccess ? '<div class="add-to-cart-success">' + this.state.addToCartSuccess + '</div>' : ''}
              ${this.state.addToCartError ? '<div class="add-to-cart-error">' + this.state.addToCartError + '</div>' : ''}
            </div>
            <button
              class="btn-add-to-cart ${this.state.addingToCart ? 'loading' : ''}"
              onclick="this.getRootNode().host.handleAddToCart()"
              ${!this.getEffectiveAvailability() || this.state.addingToCart ? 'disabled' : ''}
            >
              ${this.state.addingToCart ? 'Aggiungendo...' : this.getEffectiveAvailability() ? 'Aggiungi al carrello' : 'Non disponibile'}
            </button>
          </div>
        </div>
      </div>
    `
  }
}

// Register the custom element
if (!customElements.get('product-detail-web-component')) {
  customElements.define(
    'product-detail-web-component',
    ProductDetailWebComponent
  )
}

export default ProductDetailWebComponent
